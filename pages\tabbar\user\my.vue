<template>
  <view class="user">
    <!-- 个人信息 -->
    <view class="status_bar">
      <!-- 这里是状态栏 -->
    </view>
    <view class="header">
      <view class="status_icon">
        <view
          class="info-icon-wrapper"
          @tap="navigateTo('/pages/tabbar/home/<USER>')"
        >
          <image
            src="/static/index/Information.png"
            mode="scaleToFill"
            style="
              width: 40rpx;
              height: 40rpx;
              margin-right: 22rpx;
              display: block;
            "
          />
          <view v-if="unreadCount > 0" class="unread-dot"></view>
        </view>
        <image
          src="/static/index/set.png"
          mode="scaleToFill"
          style="width: 40rpx; height: 40rpx"
          @tap="navigateTo('/pages/mine/set/setUp')"
        />
      </view>
      <view class="login_box" @click="userDetail">
        <view class="head-1">
          <u-image
            shape="circle"
            :lazy-load="true"
            width="100"
            height="100"
            :src="userInfo.face || userImage"
          ></u-image>
          <!-- <image :src="userInfo.face || userImage" mode="scaleToFill"></image> -->
        </view>
        <view class="head-2" v-if="userInfo.id">
          <view class="user-time">{{ getGreeting() }}</view>
          <view class="user-name">{{ userInfo.nickName }}</view>
        </view>
        <view class="head-2" v-else>
          <view class="user-name">登录/注册</view>
        </view>
      </view>
      <!-- 臻享卡模块 -->
      <view
        v-if="shouldShowCard"
        class="zhen-card-box"
        :style="
          zhenCardStatus === 'expired'
            ? 'height:224rpx;background-image:url(/static/financial/my_fina2.png);'
            : ''
        "
      >
        <!-- 过期 -->
        <template v-if="zhenCardStatus === 'expired'">
          <view class="zhen-card-expired-row">
            <view class="zhen-card-expired-title">额度已过期</view>
            <u-button
              class="zhen-card-btn"
              type="warning"
              shape="circle"
              @click="applyZhenCard"
            >
              立即激活
            </u-button>
          </view>
        </template>
        <!-- 未开通 -->
        <template v-if="zhenCardStatus === 'not_open'">
          <view>
            <view class="zhen-card-notopen-row">
              <view class="zhen-card-notopen-main">
                <view class="zhen-card-notopen-amount">
                  <text class="amount">{{ creditAmount }}</text><text class="unit">元</text>
                </view>
              </view>
              <u-button
                class="zhen-card-btn"
                type="warning"
                shape="circle"
                @click="applyZhenCard('inactive')"
              >
                立即开通
              </u-button>
            </view>
            <view class="zhen-card-notopen-descs">
              <view class="desc-item"
                ><u-icon name="checkmark-circle" color="#6B4A1A" size="28" />
                官方质检</view
              >
              <view class="desc-item"
                ><u-icon name="checkmark-circle" color="#6B4A1A" size="28" />
                售后无忧</view
              >
              <view class="desc-item"
                ><u-icon name="checkmark-circle" color="#6B4A1A" size="28" />
                资质证明</view
              >
            </view>
          </view>
        </template>
        <!-- 冻结 -->
        <template v-if="zhenCardStatus === 'frozen'">
          <view class="zhen-card-frozen-row">
            <view class="zhen-card-frozen-title">额度已冻结</view>
            <u-button
              class="zhen-card-btn"
              type="warning"
              shape="circle"
              @click="goToBill"
            >
              账单明细
            </u-button>
          </view>
          <view class="zhen-card-frozen-desc">很抱歉，您的额度已冻结</view>
        </template>
        <!-- 审核中 -->
        <template v-if="zhenCardStatus === 'review'">
          <view class="zhen-card-review-row">
            <view class="zhen-card-review-main">
              <view class="zhen-card-review-title">审核中</view>
              <view class="zhen-card-review-desc">先享后付，分期购物</view>
            </view>
            <u-button
              class="zhen-card-btn"
              type="warning"
              shape="circle"
            >
              预计1分钟
            </u-button>
          </view>
        </template>
        <!-- 审核失败 -->
        <template v-if="zhenCardStatus === 'reject' || zhenCardStatus === 'reject1'">
          <view class="zhen-card-reject-row">
            <view class="zhen-card-reject-main">
              <view class="zhen-card-reject-title">审核失败</view>
            </view>
            <u-button
              class="zhen-card-btn"
              type="warning"
              shape="circle"
               v-if="zhenCardStatus === 'reject'"
            >
             {{ enjoyCardInfo.desc }}
            </u-button>
            <u-button
              class="zhen-card-btn"
              type="warning"
              shape="circle"
              v-if="zhenCardStatus === 'reject1'"
              @click="applyZhenCard('inactive')"
            >
             重新激活
            </u-button>
          </view>
          <view class="zhen-card-reject-desc"
            >多多购物积累信用，可提升通过率</view
          >
        </template>
        <!-- 过期冻结 -->
        <template v-if="zhenCardStatus === 'expired_frozen'">
          <view class="zhen-card-expired-frozen-row">
            <view class="zhen-card-expired-frozen-left">
              <view class="zhen-card-expired-frozen-btn"  @click="applyZhenCard('reject1')"> 重新激活 </view>
              <view class="zhen-card-expired-frozen-title">已冻结</view>
              <view class="zhen-card-expired-frozen-desc">可用额度</view>
            </view>
            <view class="zhen-card-expired-frozen-right">
              <view class="zhen-card-expired-frozen-btn right_btn">
                逾期{{enjoyCardInfo.overdueDays }}天
              </view>
              <view class="zhen-card-expired-frozen-amount">
                <text class="unit">￥</text> {{ toRepayAmount }}
              </view>
              <view class="zhen-card-expired-frozen-bill">{{ activeText }}</view>
            </view>
          </view>
        </template>
        <!-- 已开通有待还 -->
        <template v-if="zhenCardStatus === 'expired_repaid0' || zhenCardStatus === 'expired_repaid' || zhenCardStatus === 'expired_repaid1' || zhenCardStatus === 'overdue'">
          <view class="zhen-card-expired-frozen-row">
            <view class="zhen-card-expired-frozen-left">
              <view class="zhen-card-expired-frozen-title">
                <text class="unit">￥</text>{{ creditAmount }}
              </view>
              <view class="zhen-card-expired-frozen-desc">可用额度</view>
            </view>
            <view class="zhen-card-expired-frozen-right">
              <view @click="goToBill" class="zhen-card-expired-frozen-btn right_btn" style="top:-20rpx;"  v-if=" zhenCardStatus === 'expired_repaid' && !canRepay">
               账单明细
              </view>
              <view @click="goToCashier" class="zhen-card-expired-frozen-btn right_btn" style="top:-20rpx;"  v-if=" zhenCardStatus === 'expired_repaid' && canRepay">
               立即还款
              </view>
              <view class="zhen-card-expired-frozen-btn right_btn" style="top:-20rpx;"  v-if=" zhenCardStatus === 'overdue'">
               逾期{{enjoyCardInfo.overdueDays }}天
              </view>
              <view
                class="zhen-card-expired-frozen-amount"
                style="margin-top: 0;font-weight:400;"
              >
                <text class="unit">￥</text> {{ toRepayAmount }}
              </view>
              <view class="zhen-card-expired-frozen-bill">{{ activeText }}</view>
            </view>
          </view>
        </template>
        
      </view>
      <view class="my-order-box"  v-if="!shouldShowCard">
        <view class="order-header">
          <view class="order-title">我的订单</view>
          <view
            class="order-all"
            @tap.stop="navigateTo('/pages/order/myOrder?status=0')"
            >查看全部订单</view
          >
        </view>
        <view class="order-divider"></view>
        <view class="order-status-list">
          <view
            class="order-status-item"
            @tap.stop="navigateTo('/pages/order/myOrder?status=1')"
          >
            <image src="/static/img/icon1.png" mode="scaleToFill" />
            <view>待付款</view>
          </view>
          <view
            class="order-status-item"
            @tap.stop="navigateTo('/pages/order/myOrder?status=2')"
          >
            <image src="/static/img/icon2.png" mode="scaleToFill" />
            <view>待发货</view>
          </view>
          <view
            class="order-status-item"
            @tap.stop="navigateTo('/pages/order/myOrder?status=3')"
          >
            <image src="/static/img/icon3.png" mode="scaleToFill" />
            <view>待收货</view>
          </view>
          <view
            class="order-status-item"
            @tap.stop="navigateTo('/pages/order/evaluate/myEvaluate')"
          >
            <image src="/static/img/icon4.png" mode="scaleToFill" />
            <view>待评价</view>
          </view>
          <view
            class="order-status-item"
            @tap.stop="navigateTo('/pages/order/afterSales/afterSales')"
          >
            <image src="/static/img/icon5.png" mode="scaleToFill" />
            <view>退换/售后</view>
          </view>
        </view>
      </view>
      <!-- <u-icon style="display: flex;align-items: flex-start;" name="arrow-right"></u-icon> -->
    </view>
    <!-- 我的订单模块 -->
    <view class="my-order-box" v-if="shouldShowCard">
      <view class="order-header">
        <view class="order-title">我的订单</view>
        <view
          class="order-all"
          @tap.stop="navigateTo('/pages/order/myOrder?status=0')"
          >查看全部订单</view
        >
      </view>
      <view class="order-divider"></view>
      <view class="order-status-list">
        <view
          class="order-status-item"
          @tap.stop="navigateTo('/pages/order/myOrder?status=1')"
        >
          <image src="/static/img/icon1.png" mode="scaleToFill" />
          <view>待付款</view>
        </view>
        <view
          class="order-status-item"
          @tap.stop="navigateTo('/pages/order/myOrder?status=2')"
        >
          <image src="/static/img/icon2.png" mode="scaleToFill" />
          <view>待发货</view>
        </view>
        <view
          class="order-status-item"
          @tap.stop="navigateTo('/pages/order/myOrder?status=3')"
        >
          <image src="/static/img/icon3.png" mode="scaleToFill" />
          <view>待收货</view>
        </view>
        <view
          class="order-status-item"
          @tap.stop="navigateTo('/pages/order/evaluate/myEvaluate')"
        >
          <image src="/static/img/icon4.png" mode="scaleToFill" />
          <view>待评价</view>
        </view>
        <view
          class="order-status-item"
          @tap.stop="navigateTo('/pages/order/afterSales/afterSales')"
        >
          <image src="/static/img/icon5.png" mode="scaleToFill" />
          <view>退换/售后</view>
        </view>
      </view>
    </view>
    <!-- 公众号和浏览记录模块 -->
    <view
      :class="zhenCardStatus === 'expired' ? 'tool-rowInactive' : 'tool-row'"
    >
      <!-- 公众号 -->
      <view class="tool-card" @click="goToOfficialAccount">
        <view class="tool-card-header">
          <text>公众号</text>
          <u-icon
            name="arrow-right"
            size="22"
            style="margin: 0"
            color="#666666"
          ></u-icon>
        </view>
        <view class="tool-card-content wechat-bg">
          <image
            class="tool-card-img"
            src="/static/index/wx.png"
            mode="widthFix"
          />
        </view>
      </view>
      <!-- 浏览记录 -->
      <view
        class="tool-card"
        @click="navigateTo('/pages/cart/coupon/myCoupon')"
      >
        <view class="tool-card-header">
          <text>优惠券</text>
          <u-icon
            name="arrow-right"
            size="22"
            style="margin: 0"
            color="#666666"
          ></u-icon>
        </view>
        <view class="tool-card-content pink-bg">
          <image
            class="tool-card-img"
            src="/static/index/zj.png"
            mode="widthFix"
          />
        </view>
      </view>
    </view>
    <!-- <button @click="navigateToNvuePage">跳转到NVue页面</button> -->
    <!-- 积分，优惠券，关注， -->
    <div class="pointBox box">
      <!-- <u-row text-align="center" gutter="16" class="point">
        <u-col
          text-align="center"
          span="4"
          @click="navigateTo('/pages/mine/deposit/operation')"
        >
          <view>预存款</view>
          <view class="money">{{ walletNum | unitPrice }}</view>
        </u-col>

        <u-col
          text-align="center"
          span="4"
          @click="navigateTo('/pages/cart/coupon/myCoupon')"
        >
          <view>优惠券</view>
          <view>{{ couponNum || 0 }}</view>
        </u-col>

        <u-col
          text-align="center"
          span="4"
          @click="navigateTo('/pages/mine/myTracks')"
        >
          <view>足迹</view>
          <view>{{ footNum || 0 }}</view>
        </u-col>
      </u-row> -->
      <!-- 我的订单，代付款 -->
      <!-- <view class="order">
        <view
          class="order-item"
          @click="navigateTo('/pages/order/myOrder?status=1')"
        >
          <div class="bag bag2">
            <u-icon name="bag-fill" size="35" color="#fff"></u-icon>
          </div>
          <view>待付款</view>
        </view>
        <view
          class="order-item"
          @click="navigateTo('/pages/order/myOrder?status=3')"
        >
          <div class="bag bag3">
            <u-icon name="car-fill" size="35" color="#fff"></u-icon>
          </div>
          <view>待收货</view>
        </view>
        <view
          class="order-item"
          @click="navigateTo('/pages/order/evaluate/myEvaluate')"
        >
          <div class="bag bag4">
            <u-icon name="star-fill" size="35" color="#fff"></u-icon>
          </div>
          <view>待评价</view>
        </view>
        <view
          class="order-item"
          @click="navigateTo('/pages/order/afterSales/afterSales')"
        >
          <div class="bag bag5">
            <u-icon name="server-fill" size="35" color="#fff"></u-icon>
          </div>
          <view>售后</view>
        </view>
        <view
          class="order-item"
          @click="navigateTo('/pages/order/myOrder?status=0')"
        >
          <div class="bag bag1">
            <u-icon name="order" size="35" color="#fff"></u-icon>
          </div>
          <view>我的订单</view>
        </view>
      </view> -->
    </div>
    <!-- 常用工具 -->
    <u-modal
      v-model="showOfficialDialog"
      title="关注公众号"
      :show-cancel-button="true"
      :content-style="center"
      cancel-text="考虑一下"
      confirm-text="立即关注"
      confirm-color="#FF5134"
      @confirm="confirm"
    >
      <view class="slot-content">
        请在微信粘贴搜索"臻小选"公众号 点击关注享福利!
      </view>
    </u-modal>
    <!-- <tool /> -->
    <!-- 客服/投诉建议模块 -->
    <view class="service-box">
      <view class="service-item" @click="contactService">客服</view>
      <view class="service-divider"></view>
      <view
        class="service-item"
        @click="navigateTo('/pages/mine/set/complaintFeedback')"
        >投诉/建议</view
      >
    </view>
    <Guess
      v-if="cartListT && cartListT.length"
      type="cart"
      :title="cartTitle"
      ref="guessComponent"
      :goodsList="cartListT"
    />
  </view>
</template>
<script>
import tool from "@/pages/tabbar/user/utils/tool.vue";
import { getCouponsNum, getFootprintNum } from "@/api/members.js";
import { getUserWallet } from "@/api/members";
import configs from "@/config/config";
import { checkEnjoyCardStatus } from "@/api/common.js";
import Guess from "@/components/Guess.vue";
import { getHomeData } from "@/api/home";
import { messages } from "@/api/message.js";
import { mapState,mapMutations } from "vuex";
import storage from "@/utils/storage.js";
import { getDeviceInfo } from '@/api/safe.js';
const ConstId = require("@/utils/dx-constid.js")
export default {
  components: {
    tool,
    Guess,
  },
  data() {
    return {
      configs,
      userImage: configs.defaultUserPhoto,
      coverTransform: "translateY(0px)",
      coverTransition: "0s",
      moving: false,
      userInfo: {},
      couponNum: "",
      footNum: "",
      walletNum: "",
      showOfficialDialog: false,
      center: {
        padding: " 46rpx 76rpx 34rpx 76rpx",
        fontSize: "28rpx",
        lineHeight: "42rpx",
        color: "#333333",
        textAlign: "center",
      },
      cartListT: [],
      cartTitle: "",
      unreadCount: 0, // 初始为0
      // zhenCardStatus: "expired", // 'expired'表示过期,not_open:未开通,frozen:冻结,review:审核中,
      // reject:审核失败,expired_frozen:过期冻结,expired_repaid:已开通有待还
    };
  },
  computed: {
    ...mapState(["enjoyCardInfo"]),
    shouldShowCard() {
      const info = this.enjoyCardInfo;
      if (!info) return false;
      return info.showMy === true || info.hasIngLoan === true;
    },
    zhenCardStatus() {
      const info = this.enjoyCardInfo;
      if (!info || !info.status) return 'not_open';
      switch (info.status) {
        case 'NOT_LOGIN':      // 未登录
        case 'LOGIN':          // 已登录未开通
          return 'not_open';   
        case 'AUDIT_ING':      // 审核中
          return 'review';
        case 'FAIL_ING':       // 审核失败,未过拒绝期
          return 'reject';
        case 'FAIL_OUT':       // 审核失败,已过拒绝期
          return 'reject1';
        case 'PASS_NOLOAN':           // 审核通过,无借款
          return 'expired_repaid0';
        case 'PASS_HASLOAN':     // 审核通过,未到还款日
          return 'expired_repaid';
        case 'PASS_HASLOAN':       // 审核通过,已到还款日
          return 'expired_repaid';
        case 'OVERDUE':        // 逾期
          return 'overdue';
        case 'FREEZE_HAVE':    // 冻结,有待还
          return 'expired_frozen';
        case 'FREEZE_NO':      // 冻结,无代还
          return 'frozen';
        case 'OVERDATE':       // 过期
          return 'expired';
        default:
          return 'inactive';
      }
    },
    creditAmount() {
      return this.enjoyCardInfo && this.enjoyCardInfo.usableQuota != null
        ? this.enjoyCardInfo.usableQuota
        : '0.00';
    },
    toRepayAmount() {
      return this.enjoyCardInfo && this.enjoyCardInfo.waitRepayAmount != null
        ? this.enjoyCardInfo.waitRepayAmount
        : '0.00';
    },
    canRepay() {
      return this.enjoyCardInfo && this.enjoyCardInfo.canRepay != null
        ? this.enjoyCardInfo.canRepay
        : false;
    },
    activeText() {
      return this.canRepay ? '当期应还' : '当期待还';
    }
  },
  onLoad() {
    this.getCardDataList();
  },
  /**
   * 触底事件
   */
  onReachBottom() {
    console.log("触底事件");
    // 直接调用Guess组件的getData方法
    if (
      this.$refs.guessComponent &&
      typeof this.$refs.guessComponent.getData === "function"
    ) {
      this.$refs.guessComponent.getData();
    }
  },
  onShow() {
     // 检查臻享卡显示状态
		this.checkEnjoyCardStatus();
    this.userInfo = this.$options.filters.isLogin() || {};
    if (this.$options.filters.isLogin("auth")) {
      this.getUserOrderNum();
      this.getUnreadCount(); // 只在登录后调用
    } else {
      this.walletNum = 0;
      this.couponNum = 0;
      this.footNum = 0;
      this.unreadCount = 0; // 未登录时重置未读数量
    }
  },
  onPullDownRefresh() {
    this.getUserOrderNum();
    this.userInfo = this.$options.filters.isLogin();
  },
  // #ifndef MP
  onNavigationBarButtonTap(e) {
    const index = e.index;
    if (index === 0) {
      this.navigateTo("/pages/mine/set/setUp");
    }
  },
  // #endif

  mounted() {},
  methods: {
    ...mapMutations(["login", "setEnjoyCardInfo"]),
    // 新增：检查臻享卡显示状态
    async checkEnjoyCardStatus() {
      try {
        const res = await checkEnjoyCardStatus();
        if (res && res.data) {
          // 存储整个data对象
          console.log(res.data);
          const obj = {
            desc:'臻享卡',
            hasIngLoan:true,
            login:true,
            showMy:false,
            status:'PASS_HASLOAN',
            usableQuota:2000000,
            waitRepayAmount:1000,
            overdueDays:5,
            desc:'8月20号重新申请',
            canRepay:false
          }
          // this.setEnjoyCardInfo(obj);
          this.setEnjoyCardInfo(res.data.data);
        } else {
          // 接口失败时设为null
          this.setEnjoyCardInfo(null);
        }
      } catch (error) {
        console.error('检查臻享卡显示状态失败:', error);
        this.setEnjoyCardInfo(null);
      }
    },
    async getCardDataList() {
      const res = await getHomeData(this.pageParams);
      const result = JSON.parse(res.data.result.pageData);
      console.log(result);
      this.cartTitle = result.list[0].options.list[0].titleWay[0].title;
      console.log(this.cartTitle);

      this.cartListT = result.list[0].options.list[0].listWay;
    },
    /**
     * 统一跳转接口,拦截未登录路由
     * navigator标签现在默认没有转场动画，所以用view
     */
    navigateTo(url) {
      uni.navigateTo({
        url,
      });
    },
    navigateToNvuePage() {
      console.log("/pages/tabbar/know");

      uni.navigateTo({
        url: "/pages/tabbar/user/know",
      });
    },
    userDetail() {
      this.userInfo.id
        ? this.navigateTo("/pages/mine/set/personMsg")
        : this.$options.filters.navigateToLogin();
    },
    async getUserOrderNum() {
      uni.stopPullDownRefresh();

      Promise.all([
        getCouponsNum(), //优惠券
        getFootprintNum(), //浏览数量
        getUserWallet(), //预存款
      ]).then((res) => {
        this.couponNum = res[0].data.result;
        this.footNum = res[1].data.result;
        this.walletNum = res[2].data.result.memberWallet;
      });
    },
    getGreeting() {
      const hour = new Date().getHours();
      if (hour < 6) {
        return "凌晨好!";
      } else if (hour < 9) {
        return "早上好!";
      } else if (hour < 12) {
        return "上午好!";
      } else if (hour < 14) {
        return "中午好!";
      } else if (hour < 18) {
        return "下午好!";
      } else if (hour < 24) {
        return "晚上好!";
      } else {
        return "你好!";
      }
    },
    applyZhenCard(type) {
      // 跳转到臻享卡申请页面
      console.log("applyZhenCard",ConstId);
      if(type == 'inactive'){
        uni.navigateTo({
          url:'/pages/financial/idAuth',
        })
      }else{
        uni.navigateTo({
          url:'/pages/financial/faceAuth',
        })
      }
      return
      // this.navigateTo("/pages/card/apply");
      new ConstId({
				appId: '31e946b480e692dd891c3eba3e8bc196', // 唯一标识，必填
			}, (e, id) => {
				if (e) {
				console.log(e)
				return
				}
        this.getDeviceInfoData(id)
				console.log('唯一标识constId:', id)
			})
    },
    async getDeviceInfoData(id) {
      try {
        const res = await getDeviceInfo(id);
        if (res.success) {
          console.log('设备信息:', res.result);
          // 这里可以做后续处理
        } else {
          uni.showToast({ title: res.message || '获取失败', icon: 'none' });
        }
      } catch (err) {
        uni.showToast({ title: '请求异常', icon: 'none' });
      }
    },
    goToOfficialAccount() {
      this.showOfficialDialog = true;
    },
    confirm() {
      // #ifdef APP-PLUS
      // 复制公众号名到剪贴板
      uni.setClipboardData({
        data: "臻小选",
        success: () => {
          uni.showToast({
            title: "公众号名已复制，请到微信搜索关注",
            icon: "none",
          });
          // 跳转到微信
          setTimeout(() => {
            plus.runtime.openURL("weixin://");
          }, 1000);
        },
      });
      // #endif
    },
    contactService() {
      uni.navigateTo({
        url: "/pages/mine/im/index?userId=1376369067769724928",
      });
    },
    getUnreadCount() {
      // 检查用户是否登录
      if (!this.$options.filters.isLogin("auth")) {
        this.unreadCount = 0;
        return;
      }

      console.log("getUnreadCount");
      const params = {
        pageSize: 1,
        pageNumber: 1,
        memberId: this.$options.filters.isLogin().id,
        status: "UN_READY",
      };

      messages(params)
        .then((res) => {
          if (res.data.success && res.data.result && res.data.result.total) {
            this.unreadCount = res.data.result.total;
          } else {
            this.unreadCount = 0;
          }
        })
        .catch((error) => {
          console.error("获取未读消息数量失败:", error);
          this.unreadCount = 0;
        });
    },
    goToBillDetail() {
      this.navigateTo("/pages/financial/faceAuth");
    },
    goToBill(){
      this.navigateTo("/pages/financial/bill");
    },
    goToCashier(){
      this.navigateTo("/pages/financial/cashier");
    }
  },
};
</script>

<style lang="scss" scoped>
html,
body {
  overflow: auto;
}

.money {
  overflow: hidden;

  text-overflow: ellipsis;
  white-space: nowrap;
}

.user {
  .header {
    max-width: 100%;
    // height: 456rpx;
    padding: calc(4rpx + var(--status-bar-height)) 30rpx 0 4%;
    // height: calc(var(--status-bar-height) + 568rpx);
    background-size: cover;
    // border-bottom-left-radius: 30rpx;
    // border-bottom-right-radius: 30rpx;
    background-image: url("/static/img/main-bg.png");
    background-position: bottom;
    background-repeat: no-repeat;
    color: #ffffff;
    // display: flex;
    // justify-content: space-between;
    .status_icon {
      height: 88rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .u-icon {
        margin: 0;
      }
      .icon_setting {
        margin-left: 22rpx;
      }
    }
    .login_box {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .head-1 {
      text-align: center;
      // width: 152rpx;
      position: relative;
      display: flex;
      align-items: center;
      margin-right: 20rpx;
      image {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        // margin-bottom: 30rpx;
        // border: 3px solid #fff;
      }

      .edti-head {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        top: 100rpx;
        right: 0;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }

    .head-2 {
      flex: 1;
      // margin-left: 30rpx;
      // margin-top: 100rpx;
      line-height: 1;
      .user-time {
        font-weight: 500;
        font-size: 28rpx;
        color: #ffffff;
        margin-bottom: 22rpx;
      }
    }

    /deep/ .u-icon,
    .u-icon {
      margin-top: 106rpx;
    }
  }

  .pointBox {
    width: 94%;
    margin: 0 3%;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 24rpx 0 rgba($color: #f6f6f6, $alpha: 1);
  }

  .point {
    text-align: center;
    height: 160rpx;

    font-size: $font-sm;
    // #ifdef MP-WEIXIN
    padding: 24rpx;

    // #endif
    .u-col {
      view {
        color: $u-main-color;
        font-size: 28rpx;
      }

      view:last-child {
        margin-top: 8rpx;
        color: $main-color;
        font-size: $font-lg;
      }
    }
  }

  .order {
    height: 140rpx;
    text-align: center;
    font-size: $font-sm;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 3%;
    color: #999;

    .order-item {
      position: relative;
      line-height: 2em;
      width: 96rpx;

      :first-child {
        font-size: 48rpx;
        margin-bottom: 10rpx;
      }
    }
  }
}

.box {
  transform: translateY(-30rpx);
}

.user-name {
  font-size: 32rpx;
}

.bag {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  margin: 0 auto;
}

.bag1 {
  background: #ff4a48;
}

.bag2 {
  background: #ff992f;
}

.bag3 {
  background: #009ee0;
}

.bag4 {
  background: #00d5d5;
}

.bag5 {
  background: #28ccb0;
}

.my-order-box {
  width: 686rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx 0 0 0;
  margin: 20rpx auto;
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx;
}
.order-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #222;
}
.order-all {
  font-size: 26rpx;
  color: #999;
}
.order-divider {
  width: 95%;
  height: 2rpx;
  background: #f5f5f5;
  margin: 20rpx auto 0;
}
.order-status-list {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 36rpx 0 32rpx;
}
.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: #333;
  image {
    width: 52rpx;
    height: 52rpx;
    margin-bottom: 20rpx;
  }
}

.zhen-card-box {
  width: 686rpx;
  // min-height: 260rpx;
  background: url("/static/financial/my_fina1.png") no-repeat;
  background-size: 100% 100%;
  margin: 32rpx auto 0;
  padding: 126rpx 66rpx 24rpx 60rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(135, 34, 2, 0.01);
  .zhen-card-expired-row {
    display: flex;
    align-items: center;
  }
  .zhen-card-expired-title {
    flex: 1;
    font-size: 40rpx;
    color: #222;
    font-weight: 500;
  }
  .zhen-card-btn {
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%) !important;
    color: #fff !important;
    font-size: 28rpx !important;
    border: none !important;
    border-radius: 32rpx !important;
    padding: 0 32rpx !important;
    height: 64rpx !important;
    line-height: 64rpx !important;
  }
  .zhen-card-notopen-row {
    display: flex;
    align-items: center;
    height: 100%;
    margin-top: -20rpx;
  }
  .zhen-card-notopen-main {
    flex: 1;
  }
  .zhen-card-notopen-amount {
    position: relative;
    display: inline-block;
    font-size: 80rpx;
    color: #552e07;
    font-family: "DIN, DIN";
    font-weight: 500;
    .unit {
      position: absolute;
      top: 10rpx;
      left: 100%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 40rpx;
      background: #552e07;
      border-radius: 50%;
      font-size: 28rpx;
      color: #fff8ef;
      text-align: center;
      line-height: 40rpx;
      font-weight: 600;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      z-index: 1;
      /* #ifdef APP-PLUS */
      top: 20rpx;
      z-index: 99;
      /* #endif */
    }
  }
  .zhen-card-notopen-descs {
    display: flex;
    gap: 32rpx;
    justify-content: space-around;
  }
  .zhen-card-notopen-descs .desc-item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #6b4a1a;
    .u-icon {
      margin-top: 0;
      margin-right: 6rpx;
    }
  }
  .zhen-card-frozen-row {
    display: flex;
    align-items: center;
  }
  .zhen-card-frozen-title {
    flex: 1;
    font-size: 40rpx;
    color: #222;
    font-weight: 500;
  }
  .zhen-card-frozen-btn {
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%) !important;
    color: #fff !important;
    font-size: 28rpx !important;
    border: none !important;
    border-radius: 32rpx !important;
    padding: 0 32rpx !important;
    height: 64rpx !important;
    line-height: 64rpx !important;
  }
  .zhen-card-frozen-desc {
    margin-top: 30rpx;
    font-size: 24rpx;
    color: #333333;
  }
  .zhen-card-review-row {
    display: flex;
    align-items: center;
  }
  .zhen-card-review-main {
    flex: 1;
  }
  .zhen-card-review-title {
    font-size: 40rpx;
    color: #222;
    font-weight: 500;
  }
  .zhen-card-review-desc {
    margin-top: 18rpx;
    font-size: 28rpx;
    color: #333333;
  }
  .zhen-card-reject-row {
    display: flex;
    align-items: center;
  }
  .zhen-card-reject-main {
    flex: 1;
  }
  .zhen-card-reject-title {
    font-size: 40rpx;
    color: #222;
    font-weight: 500;
  }
  .zhen-card-reject-desc {
    margin-top: 18rpx;
    font-size: 24rpx;
    color: #333333;
  }
  .zhen-card-expired-frozen-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
  .zhen-card-expired-frozen-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    // margin-left: 47rpx;
  }
  .zhen-card-expired-frozen-btn {
    width: 104rpx;
    height: 34rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 6rpx 6rpx 6rpx 2rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #ffffff;
    text-align: center;
    line-height: 34rpx;
    margin-left: 94rpx;
  }
  .right_btn {
    position: absolute;
    right: -40rpx;
  }
  .zhen-card-expired-frozen-title {
    font-family: DIN, DIN;
    font-weight: 500;
    font-size: 48rpx;
    color: #333333;
    margin-bottom: 8rpx;
  }
  .zhen-card-expired-frozen-desc {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    margin-left: 12rpx;
  }
  .zhen-card-expired-frozen-activate {
    margin-bottom: 8rpx;
  }
  .zhen-card-expired-frozen-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    min-width: 180rpx;
    position: relative;
  }
  .zhen-card-expired-frozen-amount {
    font-size: 50rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 8rpx;
    margin-top: 30rpx;
  }
  .zhen-card-expired-frozen-amount .unit {
    font-weight: 500;
    font-size: 40rpx;
    color: #333333;
    margin-right: 4rpx;
  }
  .zhen-card-expired-frozen-bill {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    width: 100%;
    text-align: center;
  }
}

.tool-row,
.tool-rowInactive {
  display: flex;
  justify-content: space-between;
  width: 686rpx;
  margin: 20rpx auto 18rpx;
  // margin: 344rpx auto 18rpx;
}
.tool-rowInactive {
  // margin: 304rpx auto 18rpx;
}

.tool-card {
  width: 48%;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(246, 246, 246, 1);
  padding: 24rpx 24rpx 18rpx 24rpx;
  display: flex;
  flex-direction: column;
}
.tool-card-header {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #222;
  margin-bottom: 12rpx;
}

.tool-card-img {
  width: 292rpx;
  height: 86rpx;
}

.service-box {
  width: 686rpx;
  margin: 24rpx auto 0;
  background: #fff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 24rpx 0 rgba(246, 246, 246, 1);
  height: 80rpx;
}
.service-item {
  flex: 1;
  text-align: center;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 80rpx;
}
.service-divider {
  width: 2rpx;
  height: 40rpx;
  background: #f0f0f0;
}
/deep/ .u-model__title {
  margin-top: 40rpx;
  padding: 0;
}
/deep/ .hairline-left {
  border-left: 1px solid #f0f0f0;
}

.info-icon-wrapper {
  position: relative;
}
.unread-dot {
  position: absolute;
  top: 0;
  right: 24rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff3b30;
  border-radius: 50%;
  border: 2rpx solid #fff;
  z-index: 2;
}
</style>
