{
    "name" : "臻小选",
    "appid" : "__UNI__C045FF1",
    "description" : "",
    "versionName" : "1.0.2",
    "versionCode" : 4000505,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        /* 5+App特有相关 */
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Payment" : {},
            "Fingerprint" : {},
            "FaceID" : {},
            "Geolocation" : {},
            "Maps" : {},
            "OAuth" : {},
            "Camera" : {}
        },
        "error" : {
            /* 404错误页面*/
            "url" : "hybrid/html/error.html"
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                "minSdkVersion" : "21",
                "targetSdkVersion" : "30",
                /* android打包配置 */
                "permissions" : [
                    "",
                    "<uses-permission android:name=\"android.permission.INTERNET\" />",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />",
                    "<uses-permission android:name=\"com.asus.msa.SupplementaryDID.ACCESS\" />",
                    "<uses-permission android:name=\"com.huawei.android.launcher.permission.CHANGE_BADGE\" />",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\" />",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\" />",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "schemes" : "zxxshop",
                //安卓自添加 权限  应用市场 过度索取权限
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许"
                },
                "autoSdkPermissions" : false
            },
            //安卓自添加 权限  应用市场 过度索取权限
            "ios" : {
                "idfa" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "需要用与评论上传，头像上传功能",
                    "NSPhotoLibraryAddUsageDescription" : "保存商品图片到本地",
                    "NSFaceIDUsageDescription" : "使用面部识别进行登录",
                    "NSCameraUsageDescription" : "需要用与扫描二维码和商品评论图片拍摄",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "位置信息将用于高德地图的效果展示",
                    "NSMicrophoneUsageDescription" : "用户上传视频时需使用音频信息",
                    "NSContactsUsageDescription" : "我们需要访问您的通讯录以便为您提供联系人相关的服务。"
                },
                "urltypes" : "zxxshop",
                "dSYMs" : false,
                "appid" : "com.zhengxiaoxuan.zxx"
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wx32788b91bdb614c0"
                    }
                },
                "oauth" : {
                    "univerify" : {}
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "48e85877d7d8877661aeb7392c54deff",
                        "appkey_android" : "fb3f1ccb34616c70f068aa950f3e27df"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "48e85877d7d8877661aeb7392c54deff",
                        "appkey_android" : "fb3f1ccb34616c70f068aa950f3e27df"
                    }
                },
                "push" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "iosStyle" : "common",
                "ios" : {
                    "storyboard" : "CustomStoryboard.zip"
                },
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "static/Start.png",
                    "xhdpi" : "static/Start.png",
                    "xxhdpi" : "static/Start.png"
                }
            }
        },
        "nativePlugins" : {
            "DC-WBOCRService" : {
                "__plugin_info__" : {
                    "name" : "DC-WBOCRService",
                    "description" : "OCR 识别SDK，支持银行卡、身份证、驾驶证和行驶证识别。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=1233",
                    "android_package_name" : "com.zhengxiaoxuan.zxx",
                    "ios_bundle_id" : "com.zhengxiaoxuan.zxx",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "1233",
                    "parameters" : {}
                }
            },
            "DC-WBFaceService" : {
                "__plugin_info__" : {
                    "name" : "DC-WBFaceService",
                    "description" : "人脸识别 SDK。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=1300",
                    "android_package_name" : "com.zhengxiaoxuan.zxx",
                    "ios_bundle_id" : "com.zhengxiaoxuan.zxx",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "1300",
                    "parameters" : {}
                }
            }
        }
    },
    "permission" : {
        "scope.userLocation" : {
            "desc" : "你的位置信息将用于高德地图的效果展示"
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "usingComponents" : true,
        "appid" : "wx6f10f29075dc1b0b",
        "optimization" : {
            "subPackages" : true
        },
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : false,
            "es6" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "位置信息将用于高德地图的效果展示"
            },
            "scope.writePhotosAlbum" : {
                "desc" : "将用于保存图片"
            }
        },
        "plugins" : {},
        // 直播插件注释
        // "live-player-plugin" : {
        //     "version" : "1.3.0",
        //     "provider" : "wx2b03c6e691cd7370"
        // }
        "requiredPrivateInfos" : [ "chooseLocation", "getLocation" ]
    },
    "h5" : {
        "devServer" : {
            "disableHostCheck" : true,
            "https" : false
        },
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "sdkConfigs" : {
            "maps" : {}
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "title" : "zhenxx",
        "template" : ""
    }
}
